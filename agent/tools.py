"""Tool implementations for the LangGraph agent.

DEPRECATED: This file is kept for backward compatibility.
New tools should be added to the agent/tools/ directory.
"""

# Import everything from the new tools structure for backward compatibility
try:
    from agent.tools.registry import AGENT_TOOLS, TOOL_DESCRIPTIONS, get_tools_description, get_agent_tools, get_shared_tools
    from agent.tools.email_tool import create_email
    from agent.tools.attachment_tools import get_file_attachment, list_email_attachments
    from agent.tools.file_processing_tool import process_file_with_llm
    from agent.tools.accounting_tool import accounting, ACCOUNTING_AVAILABLE

    # For backward compatibility
    TOOLS = AGENT_TOOLS

    # Also import run_bookamat_subgraph if available
    try:
        if ACCOUNTING_AVAILABLE:
            from plugins.bookamat.graph import run_bookamat_subgraph
        else:
            run_bookamat_subgraph = None
    except ImportError:
        run_bookamat_subgraph = None

except ImportError as e:
    # Fallback if new structure is not available
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"Could not import from new tools structure: {e}")

    # Set defaults
    ACCOUNTING_AVAILABLE = False
    run_bookamat_subgraph = None
    TOOLS = []