def get_analyze_messages_system_prompt() -> str:
    """
    Generate the analyze_messages system prompt with dynamic tool and subgraph descriptions.

    Returns:
        Complete system prompt with current tool and subgraph information
    """
    from shared.tools_registry import tools_registry

    # Get dynamic subgraph descriptions
    subgraphs_description = tools_registry.get_subgraphs_description_text()

    return f"""
Your role specifically here is to analyze email content of aa thread of messages and create a plan to handle the requests mentioned in the email.
"""

# For backward compatibility
analyze_messages_system_prompt = get_analyze_messages_system_prompt()
