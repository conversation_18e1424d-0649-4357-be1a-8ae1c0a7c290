"""Accounting tool for the LangGraph agent."""

import logging
from typing import Optional
from langchain_core.tools import tool

from config.settings import settings

logger = logging.getLogger(__name__)

# Check if accounting is configured (but delay import to avoid circular imports)
if settings.accounting_software == "bookamat":
    ACCOUNTING_AVAILABLE = True
    logger.info("✅ Accounting is configured - Bookamat available")
else:
    ACCOUNTING_AVAILABLE = False
    logger.info(f"❌ Accounting is missing - software set to '{settings.accounting_software}' but not 'bookamat'")


@tool
def accounting(prompt: str, files: Optional[str] = None) -> str:
    """
    Führt Buchhaltungsoperationen über die konfigurierte Buchhaltungssoftware aus.

    Dieses Tool leitet Anfragen an die konfigurierte Buchhaltungssoftware weiter (z.B. Bookamat).
    Es kann Buchungen erstellen, Konten abrufen und andere buchhaltungsrelevante Aufgaben ausführen.

    Args:
        prompt: Detaillierte Beschreibung der gewünschten Buchhaltungsoperation
        files: Optional - <PERSON><PERSON><PERSON>z zu relevanten Date<PERSON> (z.<PERSON><PERSON>, Belege)

    Returns:
        Ergebnis der Buchhaltungsoperation in deutscher Sprache
    """
    try:
        if not ACCOUNTING_AVAILABLE:
            return f"❌ Buchhaltungssoftware '{settings.accounting_software}' ist nicht verfügbar oder nicht konfiguriert."

        # Prepare the full prompt with file information if provided
        full_prompt = prompt
        if files:
            full_prompt += f"\n\nRelevante Dateien: {files}"

        # Route to the appropriate accounting subgraph
        if settings.accounting_software == "bookamat":
            try:
                # Import here to avoid circular imports
                from plugins.bookamat.graph import run_bookamat_subgraph
                result = run_bookamat_subgraph(full_prompt)

                if result['success']:
                    return result['output']
                else:
                    error_msg = result.get('error', 'Unbekannter Fehler')
                    return f"❌ Fehler bei der Buchhaltungsoperation: {error_msg}"
            except ImportError as e:
                return f"❌ Fehler beim Importieren der Bookamat-Integration: {str(e)}"
        else:
            return f"❌ Buchhaltungssoftware '{settings.accounting_software}' wird noch nicht unterstützt."

    except Exception as e:
        error_msg = f"Fehler beim Ausführen der Buchhaltungsoperation: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"
