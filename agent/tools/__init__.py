"""Agent tools package."""

from .registry import (
    AGENT_TOOLS, TOOL_DESCRIPTIONS, get_tools_description,
    get_agent_tools, get_shared_tools
)
from .email_tool import create_email
from .attachment_tools import get_file_attachment, list_email_attachments
from .file_processing_tool import process_file_with_llm
from .accounting_tool import accounting, ACCOUNTING_AVAILABLE

# Import run_bookamat_subgraph if available (delayed to avoid circular imports)
def get_run_bookamat_subgraph():
    """Get the run_bookamat_subgraph function, importing it when needed."""
    if ACCOUNTING_AVAILABLE:
        try:
            from plugins.bookamat.graph import run_bookamat_subgraph
            return run_bookamat_subgraph
        except ImportError:
            return None
    return None

# For backward compatibility, provide the function directly
run_bookamat_subgraph = get_run_bookamat_subgraph()

# For backward compatibility
TOOLS = AGENT_TOOLS

__all__ = [
    'AGENT_TOOLS',
    'TOOLS',
    'TOOL_DESCRIPTIONS',
    'get_tools_description',
    'get_agent_tools',
    'get_shared_tools',
    'create_email',
    'get_file_attachment',
    'list_email_attachments',
    'process_file_with_llm',
    'accounting',
    'ACCOUNTING_AVAILABLE',
    'run_bookamat_subgraph'
]
