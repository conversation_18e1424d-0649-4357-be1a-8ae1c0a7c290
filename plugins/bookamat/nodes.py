"""
Nodes für den Bookamat Subgraph.
"""

import logging
import time
from typing import Dict, Any
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from .state import BookamatState
from agent.state import ExecutionPlan, PlanTask
from .client import BookamatClient, BookamatAPIError
from .tools import get_bookamat_tools, get_account_information
from config.settings import settings

logger = logging.getLogger(__name__)


class BookamatPlan(BaseModel):
    """Ausführungsplan für Bookamat-Operationen."""
    plan_summary: str = Field(description="1-Satz Zusammenfassung des Plans")
    tasks: list[PlanTask] = Field(description="Liste der auszuführenden Aufgaben")
    clarification_needed: list[str] = Field(default_factory=list, description="Benötigte Klarstellungen")
    current_task_index: int = Field(default=0, description="Index der aktuellen Aufgabe")
