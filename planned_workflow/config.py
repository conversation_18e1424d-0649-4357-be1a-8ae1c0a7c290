"""Configuration classes for the planned workflow package."""

from typing import List, Optional, Dict, Any, Callable
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool

# Import settings to get default API key
try:
    from config.settings import settings as app_settings
    DEFAULT_API_KEY = app_settings.openai_api_key
except ImportError:
    DEFAULT_API_KEY = None


class ModelConfig(BaseModel):
    """Configuration for models used in the workflow."""

    planning_model: str = Field(default="gpt-4.1", description="Model for planning phase")
    execution_model: str = Field(default="gpt-4.1-mini", description="Model for task execution")
    temperature: float = Field(default=0.3, description="Temperature for model responses")
    max_tokens: int = Field(default=1500, description="Maximum tokens for responses")
    openai_api_key: Optional[str] = Field(default=DEFAULT_API_KEY, description="OpenAI API key")


class WorkflowConfig(BaseModel):
    """Configuration for the planned workflow."""
    
    # Model configuration
    models: ModelConfig = Field(default_factory=ModelConfig)
    
    # Task limits
    max_tasks: int = Field(default=5, description="Maximum number of tasks in a plan")
    max_steps: int = Field(default=3, description="Maximum execution steps")
    
    # System prompt customization
    system_prompt_extension: str = Field(
        default="", 
        description="Additional system prompt content appended to base planning prompt"
    )
    
    # Tools and subgraphs
    tools: List[BaseTool] = Field(default_factory=list, description="Available tools for execution")
    subgraph: Optional[Callable] = Field(default=None, description="Optional subgraph for complex operations")
    
    # Workflow metadata
    workflow_name: str = Field(default="planned_workflow", description="Name of the workflow")
    description: str = Field(default="Generic planned workflow", description="Description of the workflow")
    
    # Additional configuration
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional configuration data")
    
    class Config:
        arbitrary_types_allowed = True  # Allow BaseTool and Callable types
