"""Generic tool results processing node for planned workflows."""

import time
import logging
from typing import Dict, Any, Callable

from ..state import PlannedWorkflowState
from ..config import WorkflowConfig

logger = logging.getLogger(__name__)


def create_tool_results_node(config: WorkflowConfig) -> Callable[[PlannedWorkflowState], Dict[str, Any]]:
    """
    Create a tool results processing node configured for a specific workflow.
    
    Args:
        config: Workflow configuration
        
    Returns:
        Tool results processing node function
    """
    
    def process_tool_results_node(state: PlannedWorkflowState) -> Dict[str, Any]:
        """
        Process tool results and determine next steps.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with processing results
        """
        start_time = time.time()
        
        try:
            # Get current plan and step
            plan = state.get("plan")
            current_step = state.get("current_step", 0)
            max_steps = state.get("max_steps", config.max_steps)

            if not plan or not plan.tasks:
                return {
                    "error": "No plan available for processing",
                    "processing_time": time.time() - start_time
                }
            
            # Check if we've exceeded max steps
            if current_step >= max_steps:
                logger.warning(f"Maximum steps ({max_steps}) reached in {config.workflow_name}")
                return {
                    "analysis": f"Maximum execution steps ({max_steps}) reached. Stopping execution.",
                    "needs_action": False,
                    "processing_time": time.time() - start_time
                }
            
            # Get current task
            current_task_index = plan.current_task_index
            if current_task_index >= len(plan.tasks):
                return {
                    "analysis": "All tasks completed successfully",
                    "needs_action": False,
                    "processing_time": time.time() - start_time
                }
            
            current_task = plan.tasks[current_task_index]
            
            # Mark current task as completed and move to next
            current_task.completed = True
            plan.current_task_index += 1
            
            # Increment step counter
            current_step += 1
            
            # Check if there are more tasks
            if plan.current_task_index < len(plan.tasks):
                next_task = plan.tasks[plan.current_task_index]
                logger.info(f"Task {current_task.id} completed, moving to {next_task.id}")
                
                return {
                    "plan": plan,
                    "current_step": current_step,
                    "analysis": f"Task {current_task.id} completed. Next: {next_task.id}",
                    "needs_action": True,
                    "processing_time": time.time() - start_time
                }
            else:
                logger.info(f"All tasks completed in {config.workflow_name}")
                return {
                    "plan": plan,
                    "current_step": current_step,
                    "analysis": "All tasks completed successfully",
                    "needs_action": False,
                    "processing_time": time.time() - start_time
                }
                
        except Exception as e:
            logger.error(f"Error in tool results processing: {e}")
            return {
                "error": f"Tool results processing failed: {str(e)}",
                "processing_time": time.time() - start_time
            }
    
    return process_tool_results_node
