"""Generic response formatting node for planned workflows."""

import time
import logging
from typing import Dict, Any, Callable

from ..state import PlannedWorkflowState
from ..config import WorkflowConfig

logger = logging.getLogger(__name__)


def create_format_response_node(config: WorkflowConfig) -> Callable[[PlannedWorkflowState], Dict[str, Any]]:
    """
    Create a response formatting node configured for a specific workflow.
    
    Args:
        config: Workflow configuration
        
    Returns:
        Response formatting node function
    """
    
    def format_response_node(state: PlannedWorkflowState) -> Dict[str, Any]:
        """
        Format the final response based on execution results.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with formatted output
        """
        start_time = time.time()
        
        try:
            # Check for errors first
            error = state.get("error")
            if error:
                output = f"❌ **{config.workflow_name} Error**\n\n{error}"
                return {
                    "output": output,
                    "processing_time": time.time() - start_time
                }
            
            # Get plan and analysis
            plan = state.get("plan")
            analysis = state.get("analysis", "")
            
            # Build response
            response_parts = []
            
            # Add workflow header
            response_parts.append(f"✅ **{config.workflow_name} Results**\n")
            
            # Add plan summary if available
            if plan:
                response_parts.append(f"**Plan:** {plan.plan_summary}\n")
                
                # Add clarification questions if any
                if plan.clarification_needed:
                    response_parts.append("**❓ Clarification Needed:**")
                    for question in plan.clarification_needed:
                        response_parts.append(f"- {question}")
                    response_parts.append("")
                
                # Add task status
                if plan.tasks:
                    response_parts.append("**📋 Task Status:**")
                    for task in plan.tasks:
                        status = "✅" if task.completed else "⏳"
                        response_parts.append(f"{status} **{task.id}:** {task.description}")
                        if task.deliverable:
                            response_parts.append(f"   *Expected:* {task.deliverable}")
                    response_parts.append("")
            
            # Add analysis if available
            if analysis:
                response_parts.append(f"**📊 Analysis:** {analysis}\n")
            
            # Add processing metadata
            processing_time = state.get("processing_time", 0)
            model_used = state.get("model_used", "Unknown")
            response_parts.append(f"**⚙️ Processing:** {processing_time:.2f}s using {model_used}")
            
            # Join all parts
            output = "\n".join(response_parts)
            
            logger.info(f"Formatted response for {config.workflow_name}")
            
            return {
                "output": output,
                "processing_time": time.time() - start_time
            }
            
        except Exception as e:
            logger.error(f"Error in response formatting: {e}")
            return {
                "output": f"❌ **Response Formatting Error**\n\n{str(e)}",
                "processing_time": time.time() - start_time
            }
    
    return format_response_node
