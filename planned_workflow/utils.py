"""Utility functions for the planned workflow package."""

import logging
from typing import List, Dict, Any, Optional
from langchain_core.tools import BaseTool

from .config import WorkflowConfig, ModelConfig

logger = logging.getLogger(__name__)


def create_workflow_config(
    workflow_name: str,
    description: str = "",
    tools: Optional[List[BaseTool]] = None,
    system_prompt_extension: str = "",
    max_tasks: int = 5,
    max_steps: int = 3,
    planning_model: str = "gpt-4.1",
    execution_model: str = "gpt-4.1-mini",
    **kwargs
) -> WorkflowConfig:
    """
    Create a workflow configuration with sensible defaults.
    
    Args:
        workflow_name: Name of the workflow
        description: Description of the workflow
        tools: List of available tools
        system_prompt_extension: Additional system prompt content
        max_tasks: Maximum number of tasks in a plan
        max_steps: Maximum execution steps
        planning_model: Model for planning phase
        execution_model: Model for execution phase
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured WorkflowConfig instance
    """
    model_config = ModelConfig(
        planning_model=planning_model,
        execution_model=execution_model
    )
    
    return WorkflowConfig(
        workflow_name=workflow_name,
        description=description or f"Generic workflow: {workflow_name}",
        models=model_config,
        tools=tools or [],
        system_prompt_extension=system_prompt_extension,
        max_tasks=max_tasks,
        max_steps=max_steps,
        metadata=kwargs
    )


def validate_workflow_config(config: WorkflowConfig) -> List[str]:
    """
    Validate a workflow configuration and return any issues.
    
    Args:
        config: Workflow configuration to validate
        
    Returns:
        List of validation issues (empty if valid)
    """
    issues = []
    
    # Check required fields
    if not config.workflow_name:
        issues.append("workflow_name is required")
    
    if not config.models.openai_api_key:
        issues.append("OpenAI API key is required")
    
    # Check model names
    valid_models = [
        "gpt-4.1", "gpt-4.1-mini", "gpt-4", "gpt-4-turbo", 
        "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
    ]
    
    if config.models.planning_model not in valid_models:
        issues.append(f"Invalid planning model: {config.models.planning_model}")
    
    if config.models.execution_model not in valid_models:
        issues.append(f"Invalid execution model: {config.models.execution_model}")
    
    # Check limits
    if config.max_tasks <= 0:
        issues.append("max_tasks must be positive")
    
    if config.max_steps <= 0:
        issues.append("max_steps must be positive")
    
    # Check tools
    if config.tools:
        for i, tool in enumerate(config.tools):
            if not hasattr(tool, 'name') or not hasattr(tool, 'description'):
                issues.append(f"Tool {i} is missing name or description")
    
    return issues


def get_workflow_summary(config: WorkflowConfig) -> Dict[str, Any]:
    """
    Get a summary of the workflow configuration.
    
    Args:
        config: Workflow configuration
        
    Returns:
        Summary dictionary
    """
    return {
        "name": config.workflow_name,
        "description": config.description,
        "planning_model": config.models.planning_model,
        "execution_model": config.models.execution_model,
        "max_tasks": config.max_tasks,
        "max_steps": config.max_steps,
        "tools_count": len(config.tools),
        "has_subgraph": config.subgraph is not None,
        "has_custom_prompt": bool(config.system_prompt_extension.strip())
    }
