"""Email processing logic for AI agent instructions."""

import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from agent import run_agent
from .connection import EmailConnection
from .database import EmailDatabase

logger = logging.getLogger(__name__)


class EmailProcessor:
    """Processes emails containing AI agent instructions."""
    
    def __init__(self):
        """Initialize email processor."""
        self.email_connection = EmailConnection()
        self.database = EmailDatabase()
    
    def process_new_emails(self) -> Dict[str, Any]:
        """Process all new emails since last sync.
        
        Returns:
            Processing results summary
        """
        logger.info("Starting email processing cycle")
        
        # Get last sync timestamp
        last_sync = self.database.get_last_sync_timestamp()
        
        # Connect to email server
        if not self.email_connection.connect_imap():
            return {
                "success": False,
                "error": "Failed to connect to IMAP server",
                "processed_count": 0
            }
        
        try:
            # Get new emails
            new_emails = self.email_connection.get_new_emails(last_sync)
            
            if not new_emails:
                logger.info("No new emails to process")
                return {
                    "success": True,
                    "processed_count": 0,
                    "message": "No new emails found"
                }
            
            # Process each email
            processed_count = 0
            failed_count = 0
            
            for email_data in new_emails:
                try:
                    # Check if already processed
                    if self.database.is_email_processed(email_data['uid']):
                        logger.info(f"Email {email_data['uid']} already processed, skipping")
                        continue
                    
                    # Process the email
                    success = self._process_single_email(email_data)
                    
                    if success:
                        processed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    logger.error(f"Failed to process email {email_data.get('uid', 'unknown')}: {e}")
                    failed_count += 1
                    
                    # Mark as processed with error
                    try:
                        self.database.mark_email_processed(
                            email_data,
                            thread_id="error",
                            response_sent=False,
                            error_message=str(e)
                        )
                    except Exception as db_error:
                        logger.error(f"Failed to mark email as failed: {db_error}")
            
            # Update sync timestamp
            current_time = datetime.now().isoformat()
            last_email_uid = new_emails[-1]['uid'] if new_emails else None
            self.database.update_sync_timestamp(current_time, last_email_uid)
            
            logger.info(f"Email processing completed: {processed_count} processed, {failed_count} failed")
            
            return {
                "success": True,
                "processed_count": processed_count,
                "failed_count": failed_count,
                "total_emails": len(new_emails),
                "last_sync": current_time
            }
            
        except Exception as e:
            logger.error(f"Email processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "processed_count": 0
            }
        
        finally:
            self.email_connection.disconnect()
    
    def _process_single_email(self, email_data: Dict[str, Any]) -> bool:
        """Process a single email with AI agent.
        
        Args:
            email_data: Email data dictionary
            
        Returns:
            True if processed successfully
        """
        try:
            logger.info(f"Processing email from {email_data['sender']}: {email_data['subject']}")
            
            # Generate unique thread ID for this email
            thread_id = f"email_{email_data['uid']}_{uuid.uuid4().hex[:8]}"

            # Use email body directly as input to the agent
            email_content = email_data.get('body', '').strip()

            if not email_content:
                logger.warning(f"Email {email_data['uid']} has no content")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=False,
                    error_message="Email has no content"
                )
                return False

            # Save attachments to database if any
            attachments = email_data.get('attachments', [])
            if attachments:
                self.database.save_email_attachments(email_data['uid'], attachments)
                logger.info(f"Saved {len(attachments)} attachments for email {email_data['uid']}")

            # Run the AI agent with the full email content
            agent_result = run_agent(email_content, email_data['sender'], thread_id)
            
            if not agent_result.get("success"):
                logger.error(f"Agent processing failed for email {email_data['uid']}: {agent_result.get('error')}")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=False,
                    error_message=agent_result.get('error', 'Agent processing failed')
                )
                return False
            
            # Use agent output directly without additional formatting
            response_content = agent_result.get('output', 'No response generated')
            
            # Send reply
            if not self.email_connection.connect_smtp():
                logger.error("Failed to connect to SMTP server for reply")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=False,
                    error_message="Failed to connect to SMTP server"
                )
                return False
            
            reply_sent = self.email_connection.send_reply(email_data, response_content)
            
            if reply_sent:
                logger.info(f"Successfully processed and replied to email {email_data['uid']}")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=True
                )
                return True
            else:
                logger.error(f"Failed to send reply for email {email_data['uid']}")
                self.database.mark_email_processed(
                    email_data,
                    thread_id=thread_id,
                    response_sent=False,
                    error_message="Failed to send reply email"
                )
                return False
                
        except Exception as e:
            logger.error(f"Failed to process email {email_data.get('uid', 'unknown')}: {e}")
            return False
    



    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status and statistics.
        
        Returns:
            Status information dictionary
        """
        try:
            stats = self.database.get_processing_stats()
            
            # Add connection status
            imap_connected = self.email_connection.connect_imap()
            if imap_connected:
                self.email_connection.disconnect()
            
            smtp_connected = self.email_connection.connect_smtp()
            if smtp_connected:
                self.email_connection.disconnect()
            
            stats.update({
                "imap_connection": imap_connected,
                "smtp_connection": smtp_connected,
                "status": "healthy" if (imap_connected and smtp_connected) else "degraded"
            })
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get processing status: {e}")
            return {
                "status": "error",
                "error": str(e),
                "total_processed": 0,
                "successful_responses": 0,
                "failed_processing": 0,
                "last_sync": None,
                "success_rate": 0,
                "imap_connection": False,
                "smtp_connection": False
            }
