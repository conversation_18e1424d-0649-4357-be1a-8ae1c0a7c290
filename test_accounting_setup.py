#!/usr/bin/env python3
"""
Test script to debug ACCOUNTING_AVAILABLE issue.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_env_loading():
    """Test environment variable loading."""
    print("=== ENVIRONMENT VARIABLES TEST ===")
    
    # Check if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print(f"✅ .env file exists at: {env_file.absolute()}")
    else:
        print(f"❌ .env file not found at: {env_file.absolute()}")
        return False
    
    # Load dotenv manually to check
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check key environment variables
    env_vars = {
        'ACCOUNTING_SOFTWARE': os.getenv('ACCOUNTING_SOFTWARE'),
        'BOOKAMAT_USERNAME': os.getenv('BOOKAMAT_USERNAME'),
        'BOOKAMAT_API_KEY': os.getenv('BOOKAMAT_API_KEY'),
        'BOOKAMAT_COUNTRY': os.getenv('BOOKAMAT_COUNTRY'),
        'BOOKAMAT_YEAR': os.getenv('BOOKAMAT_YEAR'),
    }
    
    print("\nEnvironment variables:")
    for key, value in env_vars.items():
        if value:
            # Mask sensitive data
            if 'API_KEY' in key or 'PASSWORD' in key:
                masked_value = value[:8] + '...' if len(value) > 8 else '***'
                print(f"  {key}: {masked_value}")
            else:
                print(f"  {key}: {value}")
        else:
            print(f"  {key}: ❌ NOT SET")
    
    return all(env_vars[key] for key in ['ACCOUNTING_SOFTWARE', 'BOOKAMAT_USERNAME', 'BOOKAMAT_API_KEY'])

def test_settings_loading():
    """Test settings loading."""
    print("\n=== SETTINGS LOADING TEST ===")
    
    try:
        from config.settings import settings
        print("✅ Settings imported successfully")
        
        print(f"  accounting_software: {settings.accounting_software}")
        print(f"  bookamat_username: {settings.bookamat_username}")
        print(f"  bookamat_api_key: {'***' if settings.bookamat_api_key else 'NOT SET'}")
        print(f"  bookamat_country: {settings.bookamat_country}")
        print(f"  bookamat_year: {settings.bookamat_year}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing settings: {e}")
        return False

def test_bookamat_import():
    """Test bookamat plugin import."""
    print("\n=== BOOKAMAT PLUGIN IMPORT TEST ===")
    
    try:
        # Test if bookamat plugin exists
        bookamat_path = Path("plugins/bookamat")
        if not bookamat_path.exists():
            print(f"❌ Bookamat plugin directory not found: {bookamat_path}")
            return False
        
        print(f"✅ Bookamat plugin directory exists: {bookamat_path}")
        
        # Test individual imports
        try:
            from plugins.bookamat.graph import run_bookamat_subgraph
            print("✅ Successfully imported run_bookamat_subgraph")
        except ImportError as e:
            print(f"❌ Failed to import run_bookamat_subgraph: {e}")
            return False
        
        try:
            from plugins.bookamat.client import BookamatClient
            print("✅ Successfully imported BookamatClient")
        except ImportError as e:
            print(f"❌ Failed to import BookamatClient: {e}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing bookamat import: {e}")
        return False

def test_accounting_available():
    """Test the ACCOUNTING_AVAILABLE flag."""
    print("\n=== ACCOUNTING_AVAILABLE TEST ===")

    try:
        # Import the tools module to check ACCOUNTING_AVAILABLE
        from agent.tools import ACCOUNTING_AVAILABLE, run_bookamat_subgraph

        print(f"ACCOUNTING_AVAILABLE: {ACCOUNTING_AVAILABLE}")
        print(f"run_bookamat_subgraph: {run_bookamat_subgraph}")

        # Also test direct import from accounting_tool
        try:
            from agent.tools.accounting_tool import ACCOUNTING_AVAILABLE as direct_available
            print(f"Direct ACCOUNTING_AVAILABLE: {direct_available}")
        except Exception as e:
            print(f"Error importing directly: {e}")

        # Test settings
        from config.settings import settings
        print(f"settings.accounting_software: {settings.accounting_software}")

        if ACCOUNTING_AVAILABLE:
            print("✅ Accounting is available")
            return True
        else:
            print("❌ Accounting is not available")
            return False

    except Exception as e:
        print(f"❌ Error checking ACCOUNTING_AVAILABLE: {e}")
        return False

def test_bookamat_connection():
    """Test actual connection to Bookamat."""
    print("\n=== BOOKAMAT CONNECTION TEST ===")
    
    try:
        from config.settings import settings
        from plugins.bookamat.client import BookamatClient
        
        if not settings.bookamat_username or not settings.bookamat_api_key:
            print("❌ Bookamat credentials not configured")
            return False
        
        client = BookamatClient(
            username=settings.bookamat_username,
            api_key=settings.bookamat_api_key,
            country=settings.bookamat_country,
        )
        
        # Test a simple API call
        result = client.get_bank_accounts()
        print(f"✅ Successfully connected to Bookamat API")
        print(f"  Found {len(result.get('results', []))} bank accounts")
        
        return True
        
    except Exception as e:
        print(f"❌ Error connecting to Bookamat: {e}")
        return False

def main():
    """Run all tests."""
    print("🔍 Testing ACCOUNTING_AVAILABLE setup...\n")
    
    tests = [
        ("Environment Variables", test_env_loading),
        ("Settings Loading", test_settings_loading),
        ("Bookamat Import", test_bookamat_import),
        ("Accounting Available", test_accounting_available),
        ("Bookamat Connection", test_bookamat_connection),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("SUMMARY")
    print("="*50)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 All tests passed! ACCOUNTING_AVAILABLE should be working.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        
        # Provide specific recommendations
        if not results.get("Environment Variables"):
            print("\n💡 Recommendation: Check your .env file configuration")
        if not results.get("Bookamat Import"):
            print("\n💡 Recommendation: Check if all required dependencies are installed")
        if not results.get("Bookamat Connection"):
            print("\n💡 Recommendation: Verify your Bookamat API credentials")

if __name__ == "__main__":
    main()
