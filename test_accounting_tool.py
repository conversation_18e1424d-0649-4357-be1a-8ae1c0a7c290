#!/usr/bin/env python3
"""
Test the accounting tool functionality.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_accounting_tool():
    """Test the accounting tool."""
    print("🧪 Testing accounting tool functionality...")
    
    try:
        from agent.tools import accounting, ACCOUNTING_AVAILABLE
        
        print(f"ACCOUNTING_AVAILABLE: {ACCOUNTING_AVAILABLE}")
        
        if not ACCOUNTING_AVAILABLE:
            print("❌ Accounting is not available")
            return False
        
        # Test a simple accounting request
        test_prompt = "Teste die Verbindung zu Bookamat und liste die verfügbaren Konten auf."
        
        print(f"\nTesting with prompt: {test_prompt}")
        result = accounting(test_prompt)
        
        print(f"\nResult: {result}")
        
        # Check if the result indicates success
        if "❌" in result:
            print("❌ Accounting tool returned an error")
            return False
        else:
            print("✅ Accounting tool executed successfully")
            return True
            
    except Exception as e:
        print(f"❌ Error testing accounting tool: {e}")
        return False

def main():
    """Run the test."""
    success = test_accounting_tool()
    
    if success:
        print("\n🎉 Accounting tool test passed!")
    else:
        print("\n⚠️ Accounting tool test failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
